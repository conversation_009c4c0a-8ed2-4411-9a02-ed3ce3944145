# 番茄钟应用开发指导规则

本目录包含了番茄钟应用开发的完整指导规则，旨在确保代码质量、架构一致性和开发效率。

## 📋 规则文件列表

### 1. [项目概览](./project-overview.md)
- 项目架构和技术栈
- 核心组件介绍
- 开发原则

### 2. [编码规范](./coding-standards.md)
- Swift 命名约定
- SwiftUI 最佳实践
- 代码组织和注释规范

### 3. [架构模式](./architecture-patterns.md)
- MVVM 架构实现
- 依赖注入模式
- 状态管理原则

### 4. [UI 设计指导](./ui-design-guidelines.md)
- 设计系统和布局原则
- 颜色和字体规范
- 动画和交互设计

### 5. [数据管理](./data-management.md)
- 数据持久化策略
- 同步架构设计
- 错误处理和性能优化

### 6. [测试与调试](./testing-debugging.md)
- 测试策略和最佳实践
- 调试技巧和工具
- 日志记录和错误监控

### 7. [平台特定](./platform-specific.md)
- macOS 和 iOS 特性
- 跨平台兼容性
- 权限管理

### 8. [安全与部署](./security-deployment.md)
- 安全最佳实践
- 部署检查清单
- 版本管理和监控

## 🚀 快速开始

1. **新功能开发**：先阅读架构模式和编码规范
2. **UI 实现**：参考 UI 设计指导和平台特定规则
3. **数据处理**：遵循数据管理指导
4. **测试调试**：使用测试与调试指导
5. **发布部署**：检查安全与部署清单

## 💡 使用建议

- 开发前先熟悉相关规则文件
- 代码审查时参考编码规范
- 遇到问题时查阅对应的指导文件
- 定期更新和完善规则内容

## 🔄 规则更新

这些规则会随着项目发展不断完善，建议：
- 定期回顾和更新规则
- 根据实际开发经验调整指导
- 团队成员共同维护规则质量

---

**遵循这些规则，让我们一起构建高质量的番茄钟应用！🍅**