# 认证失败自动登出功能实现说明

## 功能概述

在同步页面（SyncView.swift）中实现了自动检测服务端认证失败并触发登出流程的功能。当检测到token失效或认证相关错误时，系统会自动提示用户重新登录，并清理本地认证数据。

## 实现细节

### 1. SyncManager 中的错误检测机制

#### 新增属性
```swift
// 认证失败通知
@Published var authenticationFailureDetected: Bool = false
@Published var authenticationFailureMessage: String = ""
```

#### 核心方法：`handleAuthenticationError(_:)`
- 检测多种类型的认证错误：
  - **HTTP 401 状态码**：未授权访问
  - **服务器错误消息**：包含 "Authentication required"、"认证失败"、"Token expired"、"Invalid token" 等关键词
  - **SyncError**：`.notAuthenticated`、`.tokenExpired`
  - **AuthError**：`.tokenRefreshFailed`、`.notAuthenticated`

#### 错误处理集成点
在以下同步方法中集成了认证错误检测：
- `performSyncInternal()` - 主要同步流程
- `performForceOverwriteLocal()` - 强制覆盖本地数据
- `loadServerDataPreview()` - 加载服务端数据摘要
- `loadServerChangesPreview()` - 加载服务端变更预览
- `loadFullServerData()` - 加载完整服务端数据

### 2. SyncView 中的用户界面处理

#### 认证失败对话框
```swift
.alert("认证失败", isPresented: $syncManager.authenticationFailureDetected) {
    Button("重新登录") {
        Task {
            await handleAuthenticationFailure()
        }
    }
    Button("取消", role: .cancel) {
        // 重置认证失败标志
        syncManager.authenticationFailureDetected = false
        syncManager.authenticationFailureMessage = ""
    }
} message: {
    Text(syncManager.authenticationFailureMessage.isEmpty ? 
         "检测到认证失效，需要重新登录以继续使用同步功能。" : 
         syncManager.authenticationFailureMessage)
}
```

#### 自动登出流程：`handleAuthenticationFailure()`
1. **清除认证失败标志**
2. **执行登出操作**：调用 `authManager.logout()`
3. **重置同步状态**：
   - 清空同步状态和时间
   - 重置服务端数据缓存
   - 清空同步工作区
   - 重置连接状态
4. **自动切换界面**：由于 `authManager.isAuthenticated` 变为 false，UI 自动切换到未认证状态

## 支持的错误类型

### HTTP 状态码错误
- ✅ **401 Unauthorized** - 触发自动登出
- ❌ 其他状态码（404、500等）- 不触发自动登出

### 服务器错误消息
- ✅ "Authentication required"
- ✅ "认证失败"
- ✅ "Token expired"
- ✅ "Invalid token"
- ❌ 其他错误消息 - 不触发自动登出

### 应用内部错误
- ✅ `SyncError.notAuthenticated`
- ✅ `SyncError.tokenExpired`
- ✅ `AuthError.tokenRefreshFailed`
- ✅ `AuthError.notAuthenticated`

## 用户体验流程

1. **用户进行同步操作**
2. **服务端返回认证失败响应**（如401状态码或"Authentication required"消息）
3. **SyncManager 自动检测认证失败**
4. **显示认证失败对话框**，提供两个选项：
   - "重新登录"：执行自动登出并切换到登录界面
   - "取消"：关闭对话框，保持当前状态
5. **用户选择重新登录后**：
   - 清除本地认证数据
   - 重置所有同步状态
   - 界面自动切换到未认证状态
   - 用户可以重新进行认证

## 技术特点

### 🔒 安全性
- 自动检测多种认证失败情况
- 及时清理失效的认证数据
- 防止使用过期token继续操作

### 🎯 准确性
- 精确识别认证相关错误
- 避免将普通网络错误误判为认证失败
- 支持中英文错误消息检测

### 🚀 用户友好
- 自动化处理，减少用户困惑
- 清晰的错误提示信息
- 一键重新登录功能

### 🔄 完整性
- 覆盖所有同步相关的网络请求
- 完整的状态重置流程
- 与现有认证系统无缝集成

## 测试验证

通过 `test_auth_failure.swift` 脚本验证了错误检测逻辑的正确性，所有12个测试用例均通过，确保了功能的可靠性。

## 使用说明

该功能已自动集成到现有的同步系统中，无需额外配置。当用户的认证token失效时，系统会自动检测并提示用户重新登录，确保同步功能的正常使用。
