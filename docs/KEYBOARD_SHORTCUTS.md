# 键盘快捷键

## 已实现的快捷键

### 左侧边栏导航快捷键

- **Cmd + ,** - 激活设置页面
- **Cmd + 1** - 切换到计时页面
- **Cmd + 2** - 切换到日历页面  
- **Cmd + 3** - 切换到活动页面
- **Cmd + 4** - 切换到同步页面
- **Cmd + 5** - 切换到设置页面（与 Cmd + , 相同）

## 使用方法

1. 启动应用程序
2. 使用上述快捷键在不同的功能页面之间快速切换
3. 快捷键在应用程序获得焦点时生效

## 技术实现

快捷键通过在 ContentView 中添加隐藏的 Button 组件实现，每个按钮都配置了相应的 `keyboardShortcut` 修饰符。这种方法确保了快捷键在整个应用程序中都能正常工作。

## 侧边栏项目对应关系

| 快捷键 | 页面 | 说明 |
|--------|------|------|
| Cmd + 1 | 计时 | 番茄钟计时器功能 |
| Cmd + 2 | 日历 | 日历和事件管理 |
| Cmd + 3 | 活动 | 活动监控和统计 |
| Cmd + 4 | 同步 | 数据同步功能 |
| Cmd + , | 设置 | 应用程序设置 |
| Cmd + 5 | 设置 | 设置页面的备用快捷键 |
